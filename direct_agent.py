"""
Direct Input Version of Google ADK Agent

This agent accepts data directly as input parameters when called programmatically.
Accepts an array of base64 encoded images.
"""

import os
import base64
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
from google.adk.agents.llm_agent import Agent
from google.adk.models.google_llm import <PERSON>
from google.adk.models.lite_llm import <PERSON><PERSON><PERSON><PERSON>

from prompt import IMAGE_PROCESSING_PROMPT


class ContextData(BaseModel):
    """Schema for context data input"""
    user_id: str
    session_id: str
    previous_interactions: List[Dict[str, Any]] = []
    current_task: str = ""
    additional_context: Dict[str, Any] = {}


class DirectInputAgent:
    """Direct Input Agent that accepts data as parameters"""
    
    def __init__(self):
        """Initialize the agent"""
        # Create the underlying ADK agent
        self.agent = Agent(
            name="direct_input_image_processing_agent",
            model=LiteLlm(model="openai/gpt-4o-mini"),
            description=(
                "Direct input image processing agent with context analysis capabilities. "
                "Accepts structured data directly as input parameters."
                "Processes an array of base64 encoded images."
            ),
            instruction=IMAGE_PROCESSING_PROMPT,
            tools=[],
        )
    
    def process_with_context(self, images_base64: List[str], context: ContextData) -> Dict[str, Any]:
        """
        Process base64 encoded images with context information using the Google ADK agent
        
        Args:
            images_base64: List of base64 encoded images
            context: ContextData object containing user/session information
            
        Returns:
            Dict containing analysis results
        """
        # In a real implementation, you would:
        # 1. Decode the base64 images
        # 2. Prepare the input for the agent with both images and context
        # 3. Call the agent's run method
        # 4. Process the agent's response into the result format
        
        # Example of how you might prepare context for the agent:
        context_info = {
            "user_id": context.user_id,
            "session_id": context.session_id,
            "current_task": context.current_task,
            "previous_interactions": context.previous_interactions,
            "additional_context": context.additional_context
        }
        
        # In a full implementation, you would call the agent like this:
        # agent_input = f"Context: {context_info}\nProcess {len(images_base64)} images."
        # agent_response = self.agent.run(agent_input)
        
        # For now, we'll simulate the processing with a more detailed response
        result = {
            "status": "success",
            "user_id": context.user_id,
            "session_id": context.session_id,
            "analysis": f"Processed {len(images_base64)} images with context for user {context.user_id}",
            "task": context.current_task,
            "previous_interaction_count": len(context.previous_interactions),
            "image_count": len(images_base64),
            "agent_name": self.agent.name,
            "context_used": True
        }
        
        return result


def create_direct_input_agent() -> DirectInputAgent:
    """Create and return a direct input agent instance"""
    return DirectInputAgent()


# Example usage
if __name__ == "__main__":
    # Create the agent
    agent = create_direct_input_agent()
    
    # Create context data
    context = ContextData(
        user_id="user_123",
        session_id="session_456",
        current_task="Analyze UI changes",
        previous_interactions=[
            {"task": "initial_analysis", "result": "baseline established"},
            {"task": "change_detection", "result": "no significant changes"}
        ],
        additional_context={
            "user_preferences": {"detail_level": "high"},
            "application_type": "web"
        }
    )
    
    # Process with dummy base64 image data
    dummy_images_base64 = ["base64_encoded_image_data_1", "base64_encoded_image_data_2"]
    result = agent.process_with_context(dummy_images_base64, context)
    
    print("Direct Input Agent Result:")
    print(result)
