"""
Image Processing Agent  with vision capabilities.
"""

import os
from google.adk.agents.llm_agent import Agent
from google.adk.models.google_llm import Gemini
from google.adk.models.lite_llm import Li<PERSON><PERSON><PERSON>

from prompt import PHASE_1_JSON_EXTRACTION_PROMPT


def create_image_processing_agent() -> Agent:
    """
    Create an Enhanced Image Processing Agent with Historical Context Analysis.

    This agent is configured with:
    - Advanced multimodal capabilities for vision processing
    - Individual image processing with session context tracking
    - Historical analysis for UI interaction pattern detection
    - Mouse movement and UI element change analysis
    - Structured response generation with contextual insights

    Returns:
        Agent: Configured image processing agent with enhanced context analysis
    """
    
    # # Google Gemini API key
    # api_key = "AIzaSyB5jvOM9xD7fvDs8C5b4T2N-u58chACTo0"
    
    # # Set the API key in environment if not already set
    # if not os.getenv("GOOGLE_API_KEY"):
    #     os.environ["GOOGLE_API_KEY"] = api_key
    
    # Create Gemini model with vision capabilities
    # gemini_model = Gemini(
    #     model_name="gemini-2.0-flash-exp",  # Latest Gemini model with vision
    #     api_key=api_key,
    #     temperature=0.1,  # Low temperature for consistent, factual analysis
    #     max_output_tokens=4096,  # Sufficient for detailed structured responses
    # )
    
    # Create the agent with enhanced image processing and context analysis capabilities
    agent = Agent(
        name="enhanced_image_processing_agent",
        model=LiteLlm(model="openai/gpt-4o-mini"),
        # model="gemini-2.5-flash",
        # model=LiteLlm(model="anthropic/claude-sonnet-4-20250514"),
        description=(
            "Enhanced image processing agent with historical context analysis capabilities. "
            "Analyzes visual content using advanced AI vision, tracks UI interaction patterns, "
            "detects mouse movement changes, and provides comprehensive structured responses "
            "with contextual insights from previous images in the session."
        ),
        instruction=PHASE_1_JSON_EXTRACTION_PROMPT,
        tools=[],  # No additional tools needed - vision model handles analysis natively
    )
    
    return agent


# Create the root agent instance
root_agent = create_image_processing_agent()
