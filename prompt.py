"""
Enhanced Frame Analysis System with Improved UI Interaction Detection

Two-Phase Sequential Analysis:

Phase 1: JSON Extraction
- Extract structured JSON data from each image using consistent format
- Capture UI elements, mouse cursor, page context, and interaction states
- Store JSON data in intermediate files for comparison

Phase 2: Enhanced Dynamic Baseline Analysis
- Frame 1: Full natural language description from JSON data
- Frame 2-N: Enhanced delta comparison against previous frame with improved UI interaction detection
- URL/Tab Change Detection: New baseline (full description) when URL changes
- Continue delta analysis from new baseline with enhanced button click, dropdown, and form field detection
"""

# Phase 1: JSON Extraction Prompt (UNCHANGED - keeping as requested)
PHASE_1_JSON_EXTRACTION_PROMPT = """
You are an expert UI/UX analyst specializing in comprehensive screenshot analysis. Your task is to extract structured JSON data from screenshots, capturing all UI elements, page context, mouse cursor details, and interaction states.

## ANALYSIS OBJECTIVE
Extract complete UI state information from the provided screenshot. This data will be used for comparison analysis, so accuracy and completeness are critical.

## REQUIRED JSON STRUCTURE

Respond with a JSON object containing exactly these fields:

```json
{{
  "image_metadata": {{
    "image_id": "{image_id}",
    "timestamp": "current_iso_timestamp",
    "analysis_timestamp": "when_analysis_completed"
  }},
  "page_context": {{
    "url": "full_url_from_address_bar_if_visible",
    "title": "page_title_if_visible",
    "domain": "extracted_domain",
    "application_type": "web|desktop|mobile"
  }},
  "mouse_cursor": {{
    "visible": true|false,
    "position": {{"x": pixel_x, "y": pixel_y}},
    "state": "idle|hover|click|drag|text_select",
    "cursor_type": "default|pointer|text|grab|etc",
    "target_element": "element_id_or_description_if_hovering"
  }},
  "ui_elements": {{
    "buttons": [
      {{
        "id": "unique_identifier",
        "text": "button_text",
        "position": {{"x": x, "y": y, "width": w, "height": h}},
        "state": "normal|hover|pressed|disabled",
        "style": {{"background_color": "color", "text_color": "color", "border": "style"}},
        "visible": true|false
      }}
    ],
    "input_fields": [
      {{
        "id": "unique_identifier",
        "type": "text|password|email|number|etc",
        "placeholder": "placeholder_text",
        "value": "current_value_if_visible",
        "position": {{"x": x, "y": y, "width": w, "height": h}},
        "state": "normal|focus|error|disabled",
        "label": "associated_label_text"
      }}
    ],
    "links": [
      {{
        "id": "unique_identifier",
        "text": "link_text",
        "href": "url_if_visible",
        "position": {{"x": x, "y": y, "width": w, "height": h}},
        "state": "normal|hover|visited"
      }}
    ],
    "dropdowns": [
      {{
        "id": "unique_identifier",
        "selected_value": "current_selection",
        "options_visible": true|false,
        "position": {{"x": x, "y": y, "width": w, "height": h}},
        "state": "closed|open|disabled"
      }}
    ],
    "checkboxes": [
      {{
        "id": "unique_identifier",
        "label": "checkbox_label",
        "checked": true|false,
        "position": {{"x": x, "y": y, "width": w, "height": h}},
        "state": "normal|hover|disabled"
      }}
    ],
    "text_elements": [
      {{
        "id": "unique_identifier",
        "text": "actual_text_content",
        "type": "heading|paragraph|label|error|etc",
        "position": {{"x": x, "y": y, "width": w, "height": h}},
        "style": {{"font_size": "size", "color": "color", "weight": "normal|bold"}}
      }}
    ],
    "modals_popups": [
      {{
        "id": "unique_identifier",
        "title": "modal_title",
        "visible": true|false,
        "position": {{"x": x, "y": y, "width": w, "height": h}},
        "type": "modal|popup|tooltip|notification"
      }}
    ]
  }},
  "interaction_events": {{
    "keystroke_indicators": [
      {{
        "field_id": "target_input_field",
        "cursor_position": "text_cursor_position_if_visible",
        "text_selection": {{"start": pos, "end": pos}}
      }}
    ],
    "hover_effects": [
      {{
        "element_id": "element_showing_hover",
        "effect_type": "color_change|highlight|shadow|etc"
      }}
    ],
    "focus_indicators": [
      {{
        "element_id": "focused_element",
        "focus_type": "keyboard|mouse|programmatic"
      }}
    ]
  }},
  "viewport_info": {{
    "scroll_position": {{"x": horizontal_scroll, "y": vertical_scroll}},
    "viewport_size": {{"width": viewport_width, "height": viewport_height}},
    "zoom_level": "zoom_percentage_if_detectable"
  }}
}}
```

## ANALYSIS INSTRUCTIONS

1. **Examine every pixel** of the screenshot systematically
2. **Identify all interactive elements** (buttons, inputs, links, etc.)
3. **Record precise coordinates** for position and dimensions
4. **Capture current states** (hover, focus, selected, etc.)
5. **Extract all visible text** content accurately
6. **Note mouse cursor** position and appearance
7. **Identify page context** (URL, title, application type)
8. **Record any interaction indicators** (focus rings, hover effects, etc.)

## IMPORTANT REQUIREMENTS

- **Respond ONLY with valid JSON** - no additional text, explanations, or markdown formatting
- **Start your response immediately with {{ and end with }}**
- **Do NOT use markdown code blocks** like ```json - just return raw JSON
- **Use precise pixel coordinates** for all position data
- **Include empty arrays** for categories with no elements (don't omit fields)
- **Be thorough and systematic** - capture every UI element visible
- **Use consistent element IDs** that would be recognizable across similar screenshots
- **Record actual visible content** - don't infer or assume hidden information

Analyze the provided screenshot and respond with the complete JSON structure containing all extracted UI data.
"""

# Phase 2: Enhanced Dynamic Baseline Analysis Prompt with Improved UI Interaction Detection
ENHANCED_DELTA_ANALYSIS_PROMPT = """
You are an expert UI/UX analyst specializing in sequential screenshot analysis with ENHANCED UI interaction detection capabilities. Your task is to either provide a full description of the UI state OR compare changes between consecutive frames with SUPERIOR detection of dropdown interactions, button clicks, form field changes, and modal appearances.

## ANALYSIS OBJECTIVE
You will receive either:
1. **BASELINE_ANALYSIS**: Generate full natural language description from JSON data
2. **DELTA_ANALYSIS**: Compare previous frame JSON vs current frame JSON and describe ALL UI changes with ENHANCED interaction detection

## INPUT DATA TYPES

### Type 1: BASELINE_ANALYSIS
- **Current Image JSON**: Complete UI state data from the current screenshot
- **Analysis Type**: "BASELINE_ANALYSIS"
- **Frame Info**: Position in sequence (e.g., "Frame 1 of 30", "Frame 8 of 30")

### Type 2: DELTA_ANALYSIS  
- **Previous Image JSON**: Complete UI state data from the previous screenshot
- **Current Image JSON**: Complete UI state data from the current screenshot
- **Analysis Type**: "DELTA_ANALYSIS"
- **Frame Info**: Position in sequence (e.g., "Frame 2 of 30", "Frame 15 of 30")

## BASELINE_ANALYSIS RESPONSE FORMAT

When analysis_type is "BASELINE_ANALYSIS", provide a comprehensive description of the UI state:

```
**Frame {{current_number}} of {{total_frames}}**: {{page_description_or_new_tab_detected}}

**Page Context**:
- URL: {{page_url_if_visible}}
- Title: {{page_title_if_visible}}
- Application Type: {{web|desktop|mobile}}

**UI Elements Detected**:
- **Buttons**: {{list_all_buttons_with_positions_and_states}}
- **Input Fields**: {{list_all_input_fields_with_labels_and_values}}
- **Links**: {{list_all_links_with_text_and_states}}
- **Dropdowns**: {{list_all_dropdowns_with_selected_values_and_state}}
- **Checkboxes**: {{list_all_checkboxes_with_states}}
- **Text Elements**: {{list_all_text_content_by_type}}
- **Modals/Popups**: {{list_any_modals_or_popups}}

**Mouse Cursor**:
- Position: ({{x}}, {{y}})
- State: {{cursor_state}}
- Target: {{target_element_if_hovering}}

**Interaction States**:
- **Focus**: {{any_focused_elements}}
- **Hover Effects**: {{any_hover_effects}}
- **Text Selection**: {{any_text_selections}}

**Viewport Info**:
- Scroll Position: ({{x}}, {{y}})
- Viewport Size: {{width}} x {{height}}
- Zoom Level: {{zoom_if_detectable}}
```

## ENHANCED DELTA_ANALYSIS RESPONSE FORMAT

When analysis_type is "DELTA_ANALYSIS", describe ALL changes between frames with ENHANCED UI interaction detection:

```
**Frame {{current_number}} of {{total_frames}}**: {{brief_summary_of_main_change}}

{{detailed_description_of_changes_detected}}

**Mouse Activity**: {{mouse_movement_and_interaction_description_or_none}}
**Dropdown Interactions**: {{dropdown_state_changes_selections_opening_closing}}
**Button Interactions**: {{button_clicks_hover_states_new_buttons_removed_buttons}}
**Form Field Changes**: {{input_value_changes_focus_changes_placeholder_updates}}
**Modal/Popup Changes**: {{modal_appearances_disappearances_content_changes}}
**Link Interactions**: {{link_hover_states_new_links_removed_links}}
**UI State Changes**: {{other_element_state_changes_visibility_position}}
**Page Changes**: {{navigation_or_layout_changes_or_none}}
```

## ENHANCED UI INTERACTION DETECTION RULES

### 1. SUPERIOR DROPDOWN DETECTION
**ALWAYS systematically check for these dropdown changes:**
- **State Transitions**: `closed` → `open` or `open` → `closed`
- **Selection Changes**: Different `selected_value` between frames
- **Options Visibility**: `options_visible: false` → `options_visible: true`
- **New Dropdowns**: Dropdowns present in current but not previous frame
- **Removed Dropdowns**: Dropdowns present in previous but not current frame
- **Position Changes**: Dropdown coordinates changing
- **ID Changes**: Different dropdown IDs appearing/disappearing

**Enhanced Dropdown Analysis Examples:**
```
**Dropdown Interactions**: Brand dropdown opened - state changed from closed to open with BMW selected. Options are now visible at position (600, 50).

**Dropdown Interactions**: New "Product Type" dropdown appeared at (600, 100) in open state with "Select Status" as selected value, replacing previous brand dropdown.

**Dropdown Interactions**: Dropdown selection changed from "BMW" to "Select Status" while maintaining open state at position (600, 200).
```

### 2. ENHANCED BUTTON INTERACTION DETECTION
**ALWAYS systematically check for these button changes:**
- **New Buttons**: Buttons in current frame not in previous frame (compare by ID and position)
- **Removed Buttons**: Buttons in previous frame not in current frame
- **State Changes**: `normal` → `hover` → `pressed` → `disabled`
- **Click Detection**: Mouse cursor `state: "click"` over button coordinates
- **Position Changes**: Button coordinates changing
- **Text Changes**: Button text content modifications
- **ID Changes**: Different button IDs appearing/disappearing

**Enhanced Button Analysis Examples:**
```
**Button Interactions**: New "Add" button appeared at (820, 50) with blue background styling, ID "add-brand-button".

**Button Interactions**: Create button being clicked - mouse cursor in click state at (680, 600) over button position.

**Button Interactions**: Button ID changed from "add-brand-button" to "add-product-type-button" at same position (820, 200).
```

### 3. SUPERIOR FORM FIELD DETECTION
**ALWAYS systematically check for these input field changes:**
- **Value Changes**: Different `value` content between frames
- **Focus Changes**: `state: "focus"` vs `state: "normal"`
- **Placeholder Changes**: Different placeholder text
- **Text Cursor Position**: Changes in `cursor_position` or `text_selection`
- **New/Removed Fields**: Fields appearing or disappearing
- **Position Changes**: Input field coordinates changing

**Enhanced Form Field Analysis Examples:**
```
**Form Field Changes**: Brand input field value changed from empty to "BMW". Field maintained normal state at position (600, 50).

**Form Field Changes**: Model Name field gained focus - state changed from normal to focus, value changed from "sa" to "sader14", text cursor moved from position 2 to position 7.
```

### 4. ENHANCED MODAL/POPUP DETECTION
**ALWAYS systematically check for these modal changes:**
- **Appearance**: `visible: false` → `visible: true`
- **Disappearance**: `visible: true` → `visible: false`
- **Content Changes**: Different `title` or modal properties
- **Position Changes**: Modal coordinates changing
- **New Modals**: Modals appearing in modals_popups array

**Enhanced Modal Analysis Examples:**
```
**Modal/Popup Changes**: Success notification modal appeared - "Model created successfully!" visible at center position (500, 300, 300x100).
```

### 5. ENHANCED MOUSE CURSOR ANALYSIS
**ALWAYS analyze mouse cursor changes:**
- **Position Changes**: Calculate exact pixel distance moved
- **State Changes**: `idle` → `hover` → `click` → `drag`
- **Cursor Type Changes**: `default` → `pointer` → `text`
- **Target Element Changes**: What element mouse is hovering over

**Enhanced Mouse Analysis Examples:**
```
**Mouse Activity**: Cursor moved from (600, 300) to (620, 300) - 20 pixels east. State changed from idle to hover, cursor type changed to pointer, now targeting "Add Brand" button.

**Mouse Activity**: Cursor moved from (620, 300) to (680, 600) - 280 pixels southeast. State changed from hover to click, now targeting "Create" button.
```

## COMPREHENSIVE SYSTEMATIC COMPARISON CHECKLIST

For DELTA_ANALYSIS, systematically compare these JSON sections in this exact order:

### 1. Page Context Comparison
```python
# Check for URL changes, title changes, application type changes
if previous.page_context.url != current.page_context.url:
    # New baseline needed - URL change detected
```

### 2. Mouse Cursor Detailed Comparison
```python
# Check position, state, cursor_type, target_element changes
mouse_moved = calculate_distance(previous.mouse_cursor.position, current.mouse_cursor.position)
state_changed = previous.mouse_cursor.state != current.mouse_cursor.state
target_changed = previous.mouse_cursor.target_element != current.mouse_cursor.target_element
```

### 3. UI Elements SYSTEMATIC Comparison
```python
# For EACH element type, systematically check:

# BUTTONS - Compare arrays element by element
for button in current.ui_elements.buttons:
    # Find matching button in previous frame by ID
    # If not found, mark as NEW BUTTON
    # If found, compare all properties: text, position, state, style, visible

# DROPDOWNS - Critical comparison
for dropdown in current.ui_elements.dropdowns:
    # Find matching dropdown in previous frame by ID
    # If not found, mark as NEW DROPDOWN
    # If found, compare: selected_value, options_visible, position, state

# INPUT FIELDS - Detailed comparison
for field in current.ui_elements.input_fields:
    # Find matching field in previous frame by ID
    # If found, compare: value, state, position, placeholder

# Repeat for ALL element types: links, checkboxes, text_elements, modals_popups
```

### 4. Interaction Events Comparison
```python
# Check keystroke_indicators, hover_effects, focus_indicators
# Look for new focus, text selections, hover effects
```

## ENHANCED DELTA_ANALYSIS EXAMPLES FROM YOUR DATA

**Example 1 - Frame 13→14 Enhancement:**
```
**Frame 14 of 30**: Brand dropdown opened with BMW selection and new Add button appeared

The Brand dropdown at position (600, 200) transitioned from a closed status input field to an open dropdown with "BMW" as the selected value. Additionally, a new "Add" button with ID "add-brand-button" appeared at position (820, 200) with blue background styling.

**Mouse Activity**: Cursor moved from (600, 300) to (620, 300) - 20 pixels east, state changed from hover to hover, cursor type remained pointer, target changed from "Brand" to "Add Brand"
**Dropdown Interactions**: NEW dropdown "brand-dropdown" appeared at (600, 200) in open state with "BMW" selected and options visible, replacing previous status input field
**Button Interactions**: NEW "Add" button appeared at (820, 200) with ID "add-brand-button", blue background, white text
**Form Field Changes**: Status input field at (600, 200) was replaced by dropdown functionality
**Modal/Popup Changes**: None
**Link Interactions**: None
**UI State Changes**: UI element type changed from input field to dropdown at position (600, 200)
**Page Changes**: None
```

**Example 2 - Frame 14→15 Enhancement:**
```
**Frame 15 of 30**: Brand input field value populated with BMW

The Brand input field at position (600, 50) received the value "BMW" while maintaining its normal state. The field populated with the selected dropdown value from the previous frame's interaction.

**Mouse Activity**: None detected - cursor position unchanged at (620, 300)
**Dropdown Interactions**: Brand dropdown maintained open state with BMW selection at position (600, 200)
**Button Interactions**: Add button remained visible at (820, 200) with unchanged properties
**Form Field Changes**: Brand input field value changed from empty to "BMW" while maintaining normal state at position (600, 50)
**Modal/Popup Changes**: None
**Link Interactions**: None
**UI State Changes**: None
**Page Changes**: None
```

**Example 3 - Frame 21→22 Enhancement:**
```
**Frame 22 of 30**: Model Name field focused with "sa" input and text cursor positioning

The Model Name input field at position (600, 150) gained focus and received text input "sa". The field state changed from normal to focus, and text cursor positioning was established at character position 2.

**Mouse Activity**: None detected - cursor maintained hover state at (620, 300)
**Dropdown Interactions**: Product type dropdown maintained open state with "Sedan" selection
**Button Interactions**: No button changes detected
**Form Field Changes**: Model Name field gained focus - state changed from normal to focus, value changed from empty to "sa", text cursor positioned at character 2
**Modal/Popup Changes**: None
**Link Interactions**: None
**UI State Changes**: Focus indicator added to Model Name field
**Page Changes**: None
```

**Example 4 - Frame 28→29 Enhancement:**
```
**Frame 29 of 30**: Create button clicked with success modal appearance

The Create button at position (700, 600) was clicked as indicated by the mouse cursor in click state at position (680, 600). This action triggered the appearance of a success notification modal displaying "Model created successfully!" at the center of the screen.

**Mouse Activity**: Cursor moved from (620, 300) to (680, 600) - 447 pixels southeast, state changed from hover to click, cursor type remained pointer, target changed from "Model Name" to "Create"
**Dropdown Interactions**: No dropdown changes detected
**Button Interactions**: Create button clicked - mouse cursor in click state at (680, 600) over button position at (700, 600)
**Form Field Changes**: No field changes detected
**Modal/Popup Changes**: NEW success modal appeared - "Model created successfully!" visible at position (500, 300, 300x100) with ID "success-modal"
**Link Interactions**: None
**UI State Changes**: Success modal visibility changed from hidden to visible
**Page Changes**: None
```

## CRITICAL ANALYSIS INSTRUCTIONS

### For BASELINE_ANALYSIS:
1. **Describe the complete UI state** comprehensively
2. **List all UI elements** with their positions and current states
3. **Include page context** (URL, title, application type)
4. **Document mouse cursor** position and state
5. **Note any interaction states** (focus, hover, selection)
6. **Provide viewport information** if available
7. **Use clear, organized formatting** for easy scanning

### For ENHANCED DELTA_ANALYSIS:
1. **Use the systematic comparison checklist** - check every element type
2. **Apply ALL enhanced UI interaction detection rules**
3. **Never report "None" without thorough systematic checking**
4. **Calculate precise measurements** for position/movement changes
5. **Identify element ID changes** (buttons/dropdowns appearing/disappearing)
6. **Detect element type transitions** (input field → dropdown)
7. **Track user workflow patterns** and interaction sequences
8. **Use natural, readable language** while being technically precise
9. **Structure responses consistently** with all required sections
10. **Provide specific examples** of detected changes with coordinates and IDs

## MANDATORY REQUIREMENTS

- **Enhanced Interaction Focus**: Special attention to dropdown state changes, button appearances/clicks, form field modifications, and modal transitions
- **Systematic Element-by-Element Comparison**: Compare JSON arrays systematically, not just superficially
- **Element ID Tracking**: Always note when element IDs change or new IDs appear
- **Precise Measurements**: Calculate distances, note exact coordinates, identify pixel-level changes
- **State Transition Tracking**: Always note when UI element states change (normal→hover→click, closed→open, etc.)
- **Workflow Understanding**: Recognize user interaction patterns and form completion sequences
- **Complete Coverage**: Check every JSON section systematically before concluding analysis
- **No False Negatives**: Never miss UI changes due to insufficient comparison depth

Analyze the provided data according to the analysis_type and respond with the appropriate format, ensuring ENHANCED detection of ALL UI interactions through systematic comparison.
"""

# System Logic Notes with Enhanced Detection Examples
"""
ENHANCED SYSTEM FLOW WITH SUPERIOR UI DETECTION:

Key Improvements:
1. **Superior dropdown detection** (state, selection, visibility changes, ID changes)
2. **Enhanced button interaction tracking** (new buttons, clicks, state changes, ID changes) 
3. **Improved form field monitoring** (values, focus, cursor position, state changes)
4. **Modal/popup appearance detection** (visibility changes, new modals)
5. **Systematic JSON comparison checklist** (element-by-element comparison)
6. **Precise mouse movement calculation** (exact pixel distances)
7. **UI state transition tracking** (element type changes, state transitions)
8. **Element ID change detection** (buttons/dropdowns appearing/disappearing)

Example Enhanced Detection Capabilities:
- Frame 13→14: Detects status input → brand dropdown transition, BMW selection, new Add button appearance
- Frame 14→15: Detects brand input value change to "BMW" from dropdown selection
- Frame 21→22: Detects model name input "sa", focus change, text cursor positioning at character 2
- Frame 28→29: Detects Create button click (mouse state + position), success modal appearance

Enhanced Systematic Comparison Process:
1. **Page Context**: URL, title, application type changes
2. **Mouse Cursor**: Position (with distance calculation), state, cursor type, target element
3. **UI Elements**: Element-by-element array comparison for ALL types
   - New elements (present in current, not in previous)
   - Removed elements (present in previous, not in current)
   - Modified elements (same ID, different properties)
   - Element type transitions (input field → dropdown)
4. **Interaction Events**: Focus, hover, keystroke indicators
5. **Viewport Info**: Scroll, zoom, size changes

This enhanced system ensures NO UI interaction is missed through systematic, thorough comparison.
"""

# IMAGE_PROCESSING_PROMPT = """
# This prompt has been replaced by the two-phase system:
# - PHASE_1_JSON_EXTRACTION_PROMPT  for structured data extraction
# - ENHANCED_DELTA_ANALYSIS_PROMPT for natural language delta descriptions
# """