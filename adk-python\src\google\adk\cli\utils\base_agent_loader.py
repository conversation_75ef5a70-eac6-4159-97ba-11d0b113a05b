# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Base class for agent loaders."""

from __future__ import annotations

from abc import ABC
from abc import abstractmethod

from ...agents.base_agent import BaseAgent


class BaseAgentLoader(ABC):
  """Abstract base class for agent loaders."""

  @abstractmethod
  def load_agent(self, agent_name: str) -> BaseAgent:
    """Loads an instance of an agent with the given name."""

  @abstractmethod
  def list_agents(self) -> list[str]:
    """Lists all agents available in the agent loader in alphabetical order."""
