"""
Test script to demonstrate both agent implementations
"""

import base64
import json
from direct_agent import create_direct_input_agent, ContextData


def test_direct_agent():
    """Test the direct input agent"""
    print("Testing Direct Input Agent")
    print("=" * 30)
    
    # Create the agent
    agent = create_direct_input_agent()
    
    # Create context data
    context = ContextData(
        user_id="test_user_123",
        session_id="test_session_456",
        current_task="Analyze UI screenshots",
        previous_interactions=[
            {"task": "initial_analysis", "result": "baseline established"},
            {"task": "button_detection", "result": "found 5 buttons"}
        ],
        additional_context={
            "user_preferences": {"detail_level": "high", "focus_areas": ["buttons", "forms"]},
            "application_type": "web",
            "previous_analysis": "User is navigating a checkout flow"
        }
    )
    
    # Create dummy base64 image data (in real usage, this would be actual base64 encoded images)
    dummy_images_base64 = [
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    ]
    
    # Process with the agent
    result = agent.process_with_context(dummy_images_base64, context)
    
    print("Result:")
    print(json.dumps(result, indent=2))
    print()


def test_fastapi_instructions():
    """Print instructions for testing the FastAPI server"""
    print("FastAPI Server Agent Instructions")
    print("=" * 30)
    print()
    print("1. Start the server:")
    print("   python server_agent.py")
    print()
    print("2. Make a request using curl:")
    print('   curl -X POST "http://localhost:8000/analyze_image"' + chr(92))
    print('        -H "Content-Type: application/json"' + chr(92))
    print('        -d "{')
    print('              \"images_base64\": [')
    print('                \"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==\",')
    print('                \"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==\"')
    print('              ],')
    print('              \"context\": {')
    print('                \"user_id\": \"test_user_123\",')
    print('                \"session_id\": \"test_session_456\",')
    print('                \"current_task\": \"Analyze UI screenshots\",')
    print('                \"previous_interactions\": [')
    print('                  {\"task\": \"initial_analysis\", \"result\": \"baseline established\"},')
    print('                  {\"task\": \"button_detection\", \"result\": \"found 5 buttons\"}')
    print('                ],')
    print('                \"additional_context\": {')
    print('                  \"user_preferences\": {\"detail_level\": \"high\", \"focus_areas\": [\"buttons\", \"forms\"]},')
    print('                  \"application_type\": \"web\",')
    print('                  \"previous_analysis\": \"User is navigating a checkout flow\"')
    print('                }')
    print('              }')
    print('            }"')
    print()
    print("3. Check server health:")
    print('   curl -X GET "http://localhost:8000/health"')
    print()

if __name__ == "__main__":
    test_direct_agent()
    test_fastapi_instructions()
