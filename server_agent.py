"""
FastAPI Server Version of Google ADK Agent

This agent runs as a FastAPI web server and accepts input data through HTTP requests.
Accepts an array of base64 encoded images.
"""

import os
import base64
from typing import Dict, Any, List, Optional
from pydantic import BaseModel
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from google.adk.agents.llm_agent import Agent
from google.adk.models.google_llm import Gemini
from google.adk.models.lite_llm import Li<PERSON><PERSON><PERSON>

from prompt import IMAGE_PROCESSING_PROMPT


class ContextData(BaseModel):
    """Schema for context data input"""
    user_id: str
    session_id: str
    previous_interactions: List[Dict[str, Any]] = []
    current_task: str = ""
    additional_context: Dict[str, Any] = {}


class ImageAnalysisRequest(BaseModel):
    """Schema for image analysis request"""
    images_base64: List[str]  # Array of base64 encoded image data
    context: ContextData
    

class ImageAnalysisResponse(BaseModel):
    """Schema for image analysis response"""
    status: str
    user_id: str
    session_id: str
    analysis: str
    task: str
    previous_interaction_count: int
    image_count: int
    detailed_results: Optional[Dict[str, Any]] = None


class FastAPIServerAgent:
    """FastAPI Server Agent that accepts data through HTTP requests"""
    
    def __init__(self):
        """Initialize the agent"""
        # Create the underlying ADK agent
        self.agent = Agent(
            name="fastapi_image_processing_agent",
            model=LiteLlm(model="openai/gpt-4o-mini"),
            description=(
                "FastAPI server image processing agent with context analysis capabilities. "
                "Accepts structured data through HTTP requests."
                "Processes an array of base64 encoded images."
            ),
            instruction=IMAGE_PROCESSING_PROMPT,
            tools=[],
        )
    
    def process_with_context(self, images_base64: List[str], context: ContextData) -> ImageAnalysisResponse:
        """
        Process base64 encoded images with context information using the Google ADK agent
        
        Args:
            images_base64: List of base64 encoded images
            context: ContextData object containing user/session information
            
        Returns:
            ImageAnalysisResponse containing analysis results
        """
        # In a real implementation, you would:
        # 1. Decode the base64 images
        # 2. Prepare the input for the agent with both images and context
        # 3. Call the agent's run method
        # 4. Process the agent's response into the ImageAnalysisResponse format
        
        # Example of how you might prepare context for the agent:
        context_info = {
            "user_id": context.user_id,
            "session_id": context.session_id,
            "current_task": context.current_task,
            "previous_interactions": context.previous_interactions,
            "additional_context": context.additional_context
        }
        
        # In a full implementation, you would call the agent like this:
        # agent_input = f"Context: {context_info}\nProcess {len(images_base64)} images."
        # agent_response = self.agent.run(agent_input)
        
        # For now, we'll simulate the processing with a more detailed response
        result = ImageAnalysisResponse(
            status="success",
            user_id=context.user_id,
            session_id=context.session_id,
            analysis=f"Processed {len(images_base64)} images with context for user {context.user_id}",
            task=context.current_task,
            previous_interaction_count=len(context.previous_interactions),
            image_count=len(images_base64),
            detailed_results={
                "user_preferences": context.additional_context.get("user_preferences", {}),
                "application_type": context.additional_context.get("application_type", "unknown"),
                "agent_name": self.agent.name,
                "context_used": True
            }
        )
        
        return result


# Create FastAPI app
app = FastAPI(title="Google ADK Image Processing Agent", version="1.0.0")

# Global agent instance
server_agent = FastAPIServerAgent()


@app.post("/analyze_image", response_model=ImageAnalysisResponse)
async def analyze_image(request: ImageAnalysisRequest):
    """
    Analyze images with context data
    
    Args:
        request: ImageAnalysisRequest containing base64 encoded images and context
        
    Returns:
        ImageAnalysisResponse with analysis results
    """
    try:
        result = server_agent.process_with_context(request.images_base64, request.context)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "agent_name": "fastapi_image_processing_agent"}


# Example usage documentation
EXAMPLE_REQUEST = {
    "images_base64": ["base64_encoded_image_data_1", "base64_encoded_image_data_2"],
    "context": {
        "user_id": "user_123",
        "session_id": "session_456",
        "current_task": "Analyze UI changes",
        "previous_interactions": [
            {"task": "initial_analysis", "result": "baseline established"},
            {"task": "change_detection", "result": "no significant changes"}
        ],
        "additional_context": {
            "user_preferences": {"detail_level": "high"},
            "application_type": "web"
        }
    }
}

EXAMPLE_RESPONSE = {
    "status": "success",
    "user_id": "user_123",
    "session_id": "session_456",
    "analysis": "Processed 2 images with context for user user_123",
    "task": "Analyze UI changes",
    "previous_interaction_count": 2,
    "image_count": 2,
    "detailed_results": {
        "user_preferences": {"detail_level": "high"},
        "application_type": "web"
    }
}

if __name__ == "__main__":
    # Run the server
    uvicorn.run("server_agent:app", host="0.0.0.0", port=8000, reload=True)
