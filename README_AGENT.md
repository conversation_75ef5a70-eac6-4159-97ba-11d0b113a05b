# Google ADK Agent Implementations

This repository contains two implementations of a Google ADK agent that can handle context data:

1. **Direct Input Version** - Accepts data directly as input parameters
2. **FastAPI Server Version** - Runs as a web server and accepts data through HTTP requests

Both implementations now accept an array of base64 encoded images rather than single images.

## Direct Input Version

### File: `direct_agent.py`

This implementation allows you to call the agent directly with Python code, passing context data as parameters.

### Key Components:

- `ContextData` - Pydantic model defining the expected context data structure
- `DirectInputAgent` - Class that wraps the Google ADK agent
- `process_with_context()` - Method that processes an array of base64 encoded images with context

### Usage Example:

```python
from direct_agent import create_direct_input_agent, ContextData

# Create the agent
agent = create_direct_input_agent()

# Create context data
context = ContextData(
    user_id="user_123",
    session_id="session_456",
    current_task="Analyze UI changes",
    previous_interactions=[
        {"task": "initial_analysis", "result": "baseline established"}
    ],
    additional_context={
        "user_preferences": {"detail_level": "high"},
        "application_type": "web"
    }
)

# Process with array of base64 encoded images
images_base64 = ["base64_encoded_image_data_1", "base64_encoded_image_data_2"]
result = agent.process_with_context(images_base64, context)

print(result)
```

### Context Data Flow:

1. Context data is passed as a structured `ContextData` object
2. Array of base64 encoded images is passed as a parameter
3. The agent can use this context to influence its behavior
4. In a full implementation, the context would be used to modify prompts or guide analysis
5. Results are returned as a structured dictionary

## FastAPI Server Version

### File: `server_agent.py`

This implementation runs as a FastAPI web server and accepts data through HTTP requests.

### Key Components:

- `ContextData` - Pydantic model defining the expected context data structure
- `ImageAnalysisRequest` - Pydantic model for the HTTP request payload (with array of base64 images)
- `ImageAnalysisResponse` - Pydantic model for the HTTP response
- FastAPI endpoints for processing images and health checks

### API Endpoints:

1. `POST /analyze_image` - Process an array of images with context data
2. `GET /health` - Health check endpoint

### Usage Example:

Start the server:
```bash
python server_agent.py
```

Make a request:
```bash
curl -X POST "http://localhost:8000/analyze_image" \
     -H "Content-Type: application/json" \
     -d '{
           "images_base64": ["base64_encoded_image_data_1", "base64_encoded_image_data_2"],
           "context": {
             "user_id": "user_123",
             "session_id": "session_456",
             "current_task": "Analyze UI changes",
             "previous_interactions": [
               {"task": "initial_analysis", "result": "baseline established"}
             ],
             "additional_context": {
               "user_preferences": {"detail_level": "high"},
               "application_type": "web"
             }
           }
         }'
```

### Context Data Flow:

1. Context data and array of base64 encoded images are sent as part of the JSON payload in the HTTP request
2. FastAPI automatically validates and deserializes the data
3. The agent processes the data similar to the direct version
4. Results are returned as a structured JSON response

## Installation

Install required dependencies:

```bash
pip install fastapi uvicorn google-adk pydantic
```

Note: You may also need to install additional dependencies depending on your specific use case:
- For image processing: `opencv-python`, `numpy`, `pillow`
- For handling multipart forms: `python-multipart`

## How Context Data Flows Through Each Implementation

### Direct Input Version:
1. Context data is passed directly as a Python object
2. Array of base64 encoded images is passed as a Python list
3. The agent can immediately access all context properties
4. In a full implementation, context data would be used to influence the Google ADK agent's behavior
5. The agent's `run` method would be called with both images and context
6. Results are returned directly as Python objects

### FastAPI Server Version:
1. Context data and array of base64 encoded images are sent as JSON in the HTTP request body
2. FastAPI automatically validates and deserializes the data
3. The agent processes the data similar to the direct version
4. In a full implementation, context data would be used to influence the Google ADK agent's behavior
5. The agent's `run` method would be called with both images and context
6. Results are serialized to JSON and sent in the HTTP response

### Connection to Google ADK Agent

Both implementations create an instance of the Google ADK `Agent` class:

```python
self.agent = Agent(
    name="agent_name",
    model=LiteLlm(model="openai/gpt-4o-mini"),
    description="Agent description",
    instruction=IMAGE_PROCESSING_PROMPT,
    tools=[],
)
```

In a full implementation, the `process_with_context` method would:
1. Decode base64 images into a format the agent can process
2. Prepare context data to influence the agent's behavior
3. Call `self.agent.run()` with the combined input
4. Process the agent's response into the appropriate response format

The current implementation simulates this process but doesn't actually call the agent. To fully implement this, you would need to:
1. Decode the base64 images
2. Convert images to a format the agent can process
3. Combine images and context into a prompt
4. Call `self.agent.run()` with the prompt
5. Parse the agent's response into the response format

Both implementations use the same underlying Google ADK agent structure but provide different interfaces for passing context data and arrays of images.
