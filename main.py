from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import cv2
import numpy as np
import os
import tempfile
import shutil
from typing import List, Optional
import base64
from io import Bytes<PERSON>
from PIL import Image
import hashlib
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Video Frame Extractor", version="1.0.0")

# Add CORS middleware to handle any CORS issues
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configuration for saving frames
FRAMES_OUTPUT_DIR = "extracted_frames"
os.makedirs(FRAMES_OUTPUT_DIR, exist_ok=True)

class FrameExtractor:
    def __init__(self, similarity_threshold: float = 0.85):
        self.similarity_threshold = similarity_threshold
    
    def calculate_frame_hash(self, frame: np.ndarray) -> str:
        """Calculate a hash for the frame to detect exact duplicates"""
        # Use a slightly larger size for better hash accuracy
        small_frame = cv2.resize(frame, (128, 128))
        gray = cv2.cvtColor(small_frame, cv2.COLOR_BGR2GRAY)
        return hashlib.md5(gray.tobytes()).hexdigest()
    
    def calculate_histogram_similarity(self, frame1: np.ndarray, frame2: np.ndarray) -> float:
        """Calculate histogram-based similarity between two frames"""
        # Convert to HSV for better color representation
        hsv1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2HSV)
        hsv2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2HSV)
        
        # Calculate histograms
        hist1 = cv2.calcHist([hsv1], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])
        hist2 = cv2.calcHist([hsv2], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])
        
        # Calculate correlation coefficient
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        return correlation
    
    def are_frames_similar(self, frame1: np.ndarray, frame2: np.ndarray, threshold: float = 0.92) -> bool:
        """Check if two frames are too similar using multiple methods"""
        # Resize frames for faster comparison
        frame1_small = cv2.resize(frame1, (160, 120))
        frame2_small = cv2.resize(frame2, (160, 120))
        
        # Method 1: Histogram similarity
        hist_similarity = self.calculate_histogram_similarity(frame1_small, frame2_small)
        
        # Method 2: Structural similarity (SSIM) approximation using MSE
        gray1 = cv2.cvtColor(frame1_small, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(frame2_small, cv2.COLOR_BGR2GRAY)
        
        # Calculate normalized cross-correlation
        mean1 = np.mean(gray1)
        mean2 = np.mean(gray2)
        
        if mean1 == 0 and mean2 == 0:
            return True
        
        # Use template matching for similarity
        result = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)
        template_similarity = np.max(result)
        
        # Combine both similarities
        combined_similarity = (hist_similarity + template_similarity) / 2
        
        # Log similarity for debugging
        logger.info(f"Frame similarity: hist={hist_similarity:.3f}, template={template_similarity:.3f}, combined={combined_similarity:.3f}")
        
        return combined_similarity > threshold
    
    def extract_unique_frames(self, video_path: str, save_to_disk: bool = False, output_folder: str = None, 
                            interval_seconds: int = 1, similarity_threshold: float = 0.90) -> List[dict]:
        """Extract frames at specified intervals, removing very similar frames"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError("Could not open video file")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps
        
        logger.info(f"Video properties: FPS={fps:.2f}, Total frames={total_frames}, Duration={duration:.2f}s")
        
        frames_data = []
        previous_frame = None
        frame_hashes = set()
        
        # Create output folder if saving to disk
        if save_to_disk and output_folder:
            os.makedirs(output_folder, exist_ok=True)
        
        # Calculate total expected frames to extract
        expected_frames = int(duration / interval_seconds) + 1
        logger.info(f"Expected to extract approximately {expected_frames} frames at {interval_seconds}s intervals")
        
        # Extract frames at specified intervals
        for second in range(0, int(duration) + 1, interval_seconds):
            frame_number = int(second * fps)
            
            # Ensure we don't exceed total frames
            if frame_number >= total_frames:
                break
            
            # Set video position to the specific frame
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            ret, frame = cap.read()
            if not ret:
                logger.warning(f"Could not read frame at second {second}")
                continue
            
            # Calculate frame hash for exact duplicate detection
            frame_hash = self.calculate_frame_hash(frame)
            
            # Skip exact duplicates (same hash)
            if frame_hash in frame_hashes:
                logger.info(f"Skipping exact duplicate at second {second}")
                continue
            
            # Check similarity with previous frame (if exists)
            if previous_frame is not None:
                if self.are_frames_similar(frame, previous_frame, similarity_threshold):
                    logger.info(f"Skipping similar frame at second {second}")
                    continue
            
            # Frame is unique enough, add it to results
            frame_hashes.add(frame_hash)
            
            # Save frame to disk if requested
            frame_filename = None
            frame_path = None
            if save_to_disk and output_folder:
                frame_filename = f'frame_{second:04d}s_{frame_number:06d}.jpg'
                frame_path = os.path.join(output_folder, frame_filename)
                cv2.imwrite(frame_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            # Convert frame to base64 for JSON response
            _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
            frame_base64 = base64.b64encode(buffer).decode('utf-8')
            
            frame_info = {
                'timestamp': second,
                'frame_number': frame_number,
                'frame_data': frame_base64,
                'width': frame.shape[1],
                'height': frame.shape[0]
            }
            
            # Add file info if saved to disk
            if save_to_disk and frame_filename:
                frame_info.update({
                    'saved_filename': frame_filename,
                    'saved_path': frame_path,
                    'file_size_bytes': os.path.getsize(frame_path) if frame_path else None
                })
            
            frames_data.append(frame_info)
            previous_frame = frame.copy()
            
            logger.info(f"Extracted frame at second {second} (frame #{frame_number})")
        
        cap.release()
        logger.info(f"Total frames extracted: {len(frames_data)}")
        return frames_data

# Initialize frame extractor
extractor = FrameExtractor()

@app.post("/extract-frames/")
@app.post("/extract-frames")
async def extract_frames(
    file: UploadFile = File(..., description="Video file to process"),
    save_to_local: Optional[bool] = Form(True, description="Save frames to local storage"),
    interval_seconds: Optional[int] = Form(1, description="Interval between frames in seconds"),
    similarity_threshold: Optional[float] = Form(0.90, description="Similarity threshold (0.0-1.0)")
):
    """
    Extract unique frames from video at specified intervals
    Optionally save frames to local folder
    """
    logger.info(f"Received file: {file.filename}, Content-Type: {file.content_type}")
    logger.info(f"Parameters: interval={interval_seconds}s, similarity_threshold={similarity_threshold}")
    
    # Validate parameters
    if interval_seconds < 1:
        raise HTTPException(status_code=400, detail="Interval must be at least 1 second")
    
    if not 0.0 <= similarity_threshold <= 1.0:
        raise HTTPException(status_code=400, detail="Similarity threshold must be between 0.0 and 1.0")
    
    # Validate file type
    if not file.content_type or not any(video_type in file.content_type.lower() for video_type in ['video/', 'mp4', 'avi', 'mov', 'mkv', 'wmv']):
        logger.error(f"Invalid file type: {file.content_type}")
        raise HTTPException(
            status_code=400, 
            detail=f"File must be a video file. Received: {file.content_type}"
        )
    
    # Check file size
    file_size = 0
    try:
        file_content = await file.read()
        file_size = len(file_content)
        await file.seek(0)
        logger.info(f"File size: {file_size / 1024 / 1024:.2f} MB")
        
        if file_size > 500 * 1024 * 1024:  # 500MB limit
            raise HTTPException(
                status_code=413,
                detail="File too large. Maximum size is 500MB"
            )
    except Exception as e:
        logger.error(f"Error reading file: {e}")
        raise HTTPException(status_code=400, detail="Error reading uploaded file")
    
    # Create unique folder for this video's frames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    video_name = os.path.splitext(file.filename or "video")[0]
    safe_video_name = "".join(c for c in video_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    
    output_folder = None
    if save_to_local:
        output_folder = os.path.join(FRAMES_OUTPUT_DIR, f"{safe_video_name}_{timestamp}")
        logger.info(f"Will save frames to: {output_folder}")
    
    # Create temporary file to store uploaded video
    temp_file_path = None
    try:
        # Create temp file with proper extension
        file_extension = os.path.splitext(file.filename or "video.mp4")[1] or ".mp4"
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(file_content)
            temp_file.flush()
        
        logger.info(f"Saved temp file: {temp_file_path}")
        
        # Extract frames with custom parameters
        frames = extractor.extract_unique_frames(
            temp_file_path, 
            save_to_disk=save_to_local,
            output_folder=output_folder,
            interval_seconds=interval_seconds,
            similarity_threshold=similarity_threshold
        )
        
        response_data = {
            'success': True,
            'message': f'Extracted {len(frames)} unique frames at {interval_seconds}s intervals',
            'total_frames': len(frames),
            'video_filename': file.filename,
            'video_size_mb': round(file_size / 1024 / 1024, 2),
            'extraction_parameters': {
                'interval_seconds': interval_seconds,
                'similarity_threshold': similarity_threshold
            },
            'frames': frames
        }
        
        if save_to_local and output_folder:
            response_data.update({
                'saved_to_folder': output_folder,
                'local_storage': True
            })
        
        logger.info(f"Successfully processed video: {len(frames)} frames extracted")
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Error processing video: {e}")
        # Clean up output folder if there was an error
        if output_folder and os.path.exists(output_folder):
            shutil.rmtree(output_folder)
        raise HTTPException(
            status_code=500,
            detail=f"Error processing video: {str(e)}"
        )
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
            logger.info("Cleaned up temporary file")

@app.post("/extract-frames-only-local/")
@app.post("/extract-frames-only-local")
async def extract_frames_only_local(
    file: UploadFile = File(..., description="Video file to process"),
    interval_seconds: Optional[int] = Form(1, description="Interval between frames in seconds"),
    similarity_threshold: Optional[float] = Form(0.90, description="Similarity threshold (0.0-1.0)")
):
    """
    Extract unique frames and save only to local folder (no base64 data returned)
    More memory efficient for large videos
    """
    logger.info(f"Processing file (local only): {file.filename}")
    logger.info(f"Parameters: interval={interval_seconds}s, similarity_threshold={similarity_threshold}")
    
    # Validate parameters
    if interval_seconds < 1:
        raise HTTPException(status_code=400, detail="Interval must be at least 1 second")
    
    if not 0.0 <= similarity_threshold <= 1.0:
        raise HTTPException(status_code=400, detail="Similarity threshold must be between 0.0 and 1.0")
    
    # Validate file type
    if not file.content_type or not any(video_type in file.content_type.lower() for video_type in ['video/', 'mp4', 'avi', 'mov', 'mkv', 'wmv']):
        raise HTTPException(
            status_code=400, 
            detail=f"File must be a video file. Received: {file.content_type}"
        )
    
    # Create unique folder for this video's frames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    video_name = os.path.splitext(file.filename or "video")[0]
    safe_video_name = "".join(c for c in video_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    output_folder = os.path.join(FRAMES_OUTPUT_DIR, f"{safe_video_name}_{timestamp}")
    
    temp_file_path = None
    try:
        # Read file content
        file_content = await file.read()
        file_size = len(file_content)
        
        # Create temp file
        file_extension = os.path.splitext(file.filename or "video.mp4")[1] or ".mp4"
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(file_content)
            temp_file.flush()
        
        # Create output folder
        os.makedirs(output_folder, exist_ok=True)
        logger.info(f"Created output folder: {output_folder}")
        
        # Extract frames directly to disk without base64 conversion
        cap = cv2.VideoCapture(temp_file_path)
        if not cap.isOpened():
            raise HTTPException(status_code=500, detail="Could not open video file")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps
        
        logger.info(f"Video info: {fps:.2f} FPS, {total_frames} total frames, {duration:.2f}s duration")
        
        saved_frames = []
        previous_frame = None
        frame_hashes = set()
        
        # Extract frames at specified intervals
        for second in range(0, int(duration) + 1, interval_seconds):
            frame_number = int(second * fps)
            
            if frame_number >= total_frames:
                break
                
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            ret, frame = cap.read()
            if not ret:
                continue
            
            # Check for exact duplicates
            frame_hash = extractor.calculate_frame_hash(frame)
            
            if frame_hash in frame_hashes:
                continue
            
            # Check similarity with previous frame
            if previous_frame is not None and extractor.are_frames_similar(frame, previous_frame, similarity_threshold):
                continue
            
            frame_hashes.add(frame_hash)
            
            # Save frame as image file
            frame_filename = f'frame_{second:04d}s_{frame_number:06d}.jpg'
            frame_path = os.path.join(output_folder, frame_filename)
            cv2.imwrite(frame_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            saved_frames.append({
                'timestamp': second,
                'filename': frame_filename,
                'frame_number': frame_number,
                'width': frame.shape[1],
                'height': frame.shape[0],
                'file_path': frame_path,
                'file_size_bytes': os.path.getsize(frame_path)
            })
            
            previous_frame = frame.copy()
            logger.info(f"Saved frame at second {second}")
        
        cap.release()
        logger.info(f"Extracted {len(saved_frames)} unique frames")
        
        return JSONResponse(content={
            'success': True,
            'message': f'Extracted {len(saved_frames)} unique frames to local storage',
            'total_frames': len(saved_frames),
            'video_filename': file.filename,
            'video_size_mb': round(file_size / 1024 / 1024, 2),
            'extraction_parameters': {
                'interval_seconds': interval_seconds,
                'similarity_threshold': similarity_threshold
            },
            'output_folder': output_folder,
            'frames_info': saved_frames
        })
        
    except Exception as e:
        logger.error(f"Error processing video: {e}")
        # Clean up output folder if there was an error
        if os.path.exists(output_folder):
            shutil.rmtree(output_folder)
        raise HTTPException(
            status_code=500,
            detail=f"Error processing video: {str(e)}"
        )
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

@app.get("/list-extractions/")
async def list_extractions():
    """
    List all extraction folders and their contents
    """
    try:
        extractions = []
        
        if os.path.exists(FRAMES_OUTPUT_DIR):
            for folder_name in os.listdir(FRAMES_OUTPUT_DIR):
                folder_path = os.path.join(FRAMES_OUTPUT_DIR, folder_name)
                if os.path.isdir(folder_path):
                    frame_files = [f for f in os.listdir(folder_path) if f.endswith(('.jpg', '.jpeg', '.png'))]
                    
                    folder_size = sum(
                        os.path.getsize(os.path.join(folder_path, f)) 
                        for f in frame_files
                    )
                    
                    extractions.append({
                        'folder_name': folder_name,
                        'folder_path': folder_path,
                        'frame_count': len(frame_files),
                        'total_size_bytes': folder_size,
                        'total_size_mb': round(folder_size / (1024 * 1024), 2),
                        'created_time': datetime.fromtimestamp(os.path.getctime(folder_path)).isoformat()
                    })
        
        return JSONResponse(content={
            'success': True,
            'total_extractions': len(extractions),
            'extractions': sorted(extractions, key=lambda x: x['created_time'], reverse=True)
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing extractions: {str(e)}"
        )

@app.delete("/cleanup-extractions/")
async def cleanup_extractions(older_than_hours: int = 24):
    """
    Clean up extraction folders older than specified hours
    """
    try:
        if not os.path.exists(FRAMES_OUTPUT_DIR):
            return JSONResponse(content={
                'success': True,
                'message': 'No extractions folder found',
                'deleted_folders': 0
            })
        
        deleted_count = 0
        current_time = datetime.now().timestamp()
        cutoff_time = current_time - (older_than_hours * 3600)
        
        for folder_name in os.listdir(FRAMES_OUTPUT_DIR):
            folder_path = os.path.join(FRAMES_OUTPUT_DIR, folder_name)
            if os.path.isdir(folder_path):
                folder_time = os.path.getctime(folder_path)
                if folder_time < cutoff_time:
                    shutil.rmtree(folder_path)
                    deleted_count += 1
        
        return JSONResponse(content={
            'success': True,
            'message': f'Cleaned up {deleted_count} extraction folders older than {older_than_hours} hours',
            'deleted_folders': deleted_count
        })
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error during cleanup: {str(e)}"
        )

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)