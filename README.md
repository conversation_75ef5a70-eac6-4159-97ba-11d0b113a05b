# Google ADK Agent with Context Data Handling

This project demonstrates two implementations of a Google ADK agent that can handle context data:

1. **Direct Input Version**: Accepts data directly as input parameters when called programmatically
2. **FastAPI Server Version**: Runs as a FastAPI web server and accepts input data through HTTP requests as JSON payload

## Project Structure

- `agent.py`: Contains the Google ADK agent implementation and direct input processing function
- `api.py`: FastAPI server implementation that exposes the agent as a web service
- `prompt.py`: Contains the prompt templates for the agent
- `requirements.txt`: Project dependencies

## Installation

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### 1. Direct Input Version

The direct input version allows you to call the agent programmatically with context data:

```python
from agent import create_context_aware_agent, process_request_direct

# Create the agent
agent = create_context_aware_agent()

# Define context data
context = {
    "user_preferences": {
        "language": "English",
        "tone": "formal"
    },
    "historical_data": {
        "last_query": "weather forecast"
    }
}

# Process a request with context data
response = process_request_direct(agent, "What's the weather like?", context)
print(response)
```

### 2. FastAPI Server Version

To run the FastAPI server:

```bash
python api.py
```

The server will start on `http://localhost:8000`.

#### API Endpoints

- `GET /` - Health check endpoint
- `GET /health` - Health check endpoint
- `POST /agent/process` - Process a request with the agent
- `GET /agent/info` - Get information about the agent

#### Example API Request

```bash
curl -X POST "http://localhost:8000/agent/process" \
     -H "Content-Type: application/json" \
     -d '{
           "user_request": "What\'s the weather like today?",
           "context_data": {
             "user_preferences": {
               "location": "New York",
               "temperature_unit": "celsius"
             },
             "historical_data": {
               "last_query": "weather forecast"
             }
           }
         }'
```

## Context Data Flow

### Direct Input Version

1. User calls `process_request_direct()` with agent, user_request, and optional context_data
2. Function formats the input combining user request and context data
3. Agent processes the formatted input
4. Function returns the agent's response

### FastAPI Server Version

1. User sends HTTP POST request to `/agent/process` with JSON payload
2. FastAPI validates the request against the AgentRequest schema
3. Server calls `process_request_direct()` with the agent and request data
4. Function processes the request and returns the response
5. FastAPI formats and returns the response to the user

## Expected Input Data Structure

### Context Data Schema

The context data is expected to be a dictionary that may contain:

- `user_preferences`: User-specific preferences (language, tone, etc.)
- `historical_data`: Historical information from previous interactions
- `environmental_info`: Environmental context (location, time, etc.)

### API Request Schema

```json
{
  "user_request": "string",
  "context_data": {
    "user_preferences": {
      "additionalProp1": "string",
      "additionalProp2": "string"
    },
    "historical_data": {
      "additionalProp1": "string",
      "additionalProp2": "string"
    },
    "environmental_info": {
      "additionalProp1": "string",
      "additionalProp2": "string"
    }
  }
}
```

## Example Usage

### Direct Input Example

```python
from agent import create_context_aware_agent, process_request_direct

agent = create_context_aware_agent()

context = {
    "user_preferences": {
        "language": "Spanish",
        "tone": "friendly"
    },
    "historical_data": {
        "previous_requests": ["hello", "how are you"]
    }
}

response = process_request_direct(agent, "Tell me a joke", context)
print(response)
```

### API Example

```bash
curl -X POST "http://localhost:8000/agent/process" \
     -H "Content-Type: application/json" \
     -d '{
           "user_request": "Tell me a joke",
           "context_data": {
             "user_preferences": {
               "language": "Spanish",
               "tone": "friendly"
             },
             "historical_data": {
               "previous_requests": ["hello", "how are you"]
             }
           }
         }'
```

## Implementation Details

### Google ADK Integration

The agent uses the Google Agent Development Kit (ADK) framework:

- `Agent` class from `google.adk.agents.llm_agent`
- `LiteLlm` model wrapper for compatibility with various LLM providers
- Context-aware instruction prompting for handling user requests with additional context

### FastAPI Integration

The API server uses FastAPI for:

- RESTful API endpoints with automatic OpenAPI documentation
- Request/response validation using Pydantic models
- Error handling and proper HTTP status codes
- Asynchronous request processing

## Customization

You can customize the agent by modifying:

1. The agent's instruction prompt in `agent.py`
2. The context data schema in `api.py`
3. The API endpoints and request/response models
4. The LLM model used by changing the `LiteLlm` configuration

## Troubleshooting

If you encounter issues:

1. Ensure all dependencies are installed: `pip install -r requirements.txt`
2. Check that the Google ADK is properly configured with your API keys
3. Verify the FastAPI server is running: `python api.py`
4. Check the API documentation at `http://localhost:8000/docs`
